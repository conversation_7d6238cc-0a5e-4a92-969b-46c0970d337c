-- 数据库更新脚本，用于在site_setting表中添加大宗交易时间配置
-- 假设site_setting表的id为1

-- 添加大宗交易时间字段（如果表结构还没有这些字段的话）
ALTER TABLE site_setting ADD COLUMN dz_trans_begin VARCHAR(10) COMMENT '大宗交易开始时间';
ALTER TABLE site_setting ADD COLUMN dz_trans_end VARCHAR(10) COMMENT '大宗交易结束时间';

-- 更新大宗交易时间配置示例（15:00-15:30为大宗交易时间）
UPDATE site_setting SET 
    dz_trans_begin = '15:00',
    dz_trans_end = '15:30'
WHERE id = 1;

-- 验证字段是否添加成功
SELECT dz_trans_begin, dz_trans_end FROM site_setting WHERE id = 1;
