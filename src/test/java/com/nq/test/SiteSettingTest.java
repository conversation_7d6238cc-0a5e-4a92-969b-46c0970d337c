package com.nq.test;

import com.nq.pojo.SiteSetting;
import org.junit.Test;
import static org.junit.Assert.*;

/**
 * SiteSetting测试类
 * 用于验证大宗交易时间字段的功能
 */
public class SiteSettingTest {

    @Test
    public void testDzTransTimeFields() {
        // 测试新增的大宗交易时间字段
        SiteSetting siteSetting = new SiteSetting();
        
        // 设置大宗交易时间
        siteSetting.setDzTransBegin("15:00");
        siteSetting.setDzTransEnd("15:30");
        
        // 验证getter方法
        assertEquals("15:00", siteSetting.getDzTransBegin());
        assertEquals("15:30", siteSetting.getDzTransEnd());
        
        System.out.println("大宗交易开始时间: " + siteSetting.getDzTransBegin());
        System.out.println("大宗交易结束时间: " + siteSetting.getDzTransEnd());
    }
    
    @Test
    public void testNullDzTransTime() {
        // 测试空值情况
        SiteSetting siteSetting = new SiteSetting();
        
        assertNull(siteSetting.getDzTransBegin());
        assertNull(siteSetting.getDzTransEnd());
    }
}
