package com.nq.service.pay;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.nq.dao.UserMapper;
import com.nq.dao.UserRechargeMapper;
import com.nq.pojo.User;
import com.nq.pojo.UserRecharge;
import com.nq.utils.ip.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * 聚合支付通道实现
 *
 * <AUTHOR>
 * @since 2025/5/11 23:52
 */
//@Service
@Slf4j
public class PdjcPayService implements PayService {

    @Resource
    private UserRechargeMapper userRechargeMapper;

    @Resource
    private UserMapper userMapper;

    @Value("${pdjc.pay.app_id:s8N7Yhs7F9zklx3D6hDx3mg5}")
    private String appId;

    @Value("${pdjc.pay.app_key:QQtoyWMPd3UsN6BdnEEzCjowq1JgytwUOpfOyKNdBDcIGg2GvM2bHFFlqMxhsqYXj9FibsBGZ5MUidiHtrE19LzDPBUK9WR6gWW}")
    private String appKey;

    @Value("${pdjc.pay.agent_code:O1343}")
    private String agentCode;

    @Value("${pdjc.pay.api_url:https://api.pdjc9pm.com:28165}")
    private String apiUrl;

    @Override
    public String sendThirdPayRequest(UserRecharge userRecharge, HttpServletRequest request) {
        // 构建请求参数
        Map<String, Object> params = new LinkedHashMap<>();
        String domain = request.getHeader("host");
        if (StrUtil.isEmpty(domain)) {
            domain = request.getServerName();
        }
        String callbackUrl = "https://" + domain + "/api/pay/callback.do";
        String createIp = IpUtils.getIp(request);

        // 设置请求参数
        params.put("app_id", appId);
        params.put("agent_code", agentCode);
        params.put("out_uid", userRecharge.getUserId().toString());
        params.put("out_trade_no", userRecharge.getOrderSn());
        params.put("amount", userRecharge.getPayAmt().toString());
        // 固定值 2
        params.put("currency", "2");
        params.put("timestamp", String.valueOf(System.currentTimeMillis() / 1000));
        params.put("callback_url", callbackUrl);
        params.put("create_ip", createIp);

        // 生成签名
        String sign = generateSign(params);
        params.put("sign", sign);

        log.info("【sendThirdPayRequest】发起三方支付=>订单号：{}，请求参数：{}", userRecharge.getOrderSn(), JSONUtil.toJsonStr(params));

        // 发送请求
        String response = HttpUtil.post(apiUrl + "/Api/Pay/unionOrderChange", params);
        log.info("【sendThirdPayRequest】三方支付返回结果=>订单号：{}，返回结果：{}", userRecharge.getOrderSn(), response);

        // 解析响应
        JSONObject jsonObject = JSONUtil.parseObj(response);
        if (jsonObject.getInt("status") == 200) {
            JSONObject data = jsonObject.getJSONObject("data");
            // 保存平台订单号
            userRecharge.setPaySn(data.getStr("pay_order_id"));
            userRechargeMapper.updateByPrimaryKeySelective(userRecharge);

            // 返回支付URL
            return data.getStr("pay_url");
        }

        return null;
    }

    @Override
    public void payCallback(Map<String, Object> callbackRequestParamMap, HttpServletRequest request,
                            HttpServletResponse response) throws Exception {
        log.info("【payCallback】支付回调=>回调参数：{}", JSONUtil.toJsonStr(callbackRequestParamMap));

        // 获取回调参数
        // String platformProductId = (String)callbackRequestParamMap.get("platform_product_id");
        Integer status = Integer.valueOf(callbackRequestParamMap.get("status").toString());
        String payOrderId = (String) callbackRequestParamMap.get("pay_order_id");
        // String outUid = (String)callbackRequestParamMap.get("out_uid");
        String outTradeNo = (String) callbackRequestParamMap.get("out_trade_no");
        String amount = (String) callbackRequestParamMap.get("amount");
        // String realAmount = (String)callbackRequestParamMap.get("real_amount");
        // String cnyRate = callbackRequestParamMap.get("cny_rate").toString();
        // String exchangeRate = callbackRequestParamMap.get("exchange_rate").toString();
        // String notifyTime = (String)callbackRequestParamMap.get("notify_time");
        // String sign = (String)callbackRequestParamMap.get("sign");
        //
        // // 验证签名
        // Map<String, Object> signParams = new TreeMap<>(callbackRequestParamMap);
        // signParams.remove("sign");
        // String calculatedSign = generateSign(signParams);
        //
        // if (!calculatedSign.equals(sign)) {
        // log.error("【payCallback】支付回调=>签名验证失败，订单号：{}", outTradeNo);
        // response.getWriter().write("sign error");
        // return;
        // }

        // 查询订单
        UserRecharge userRecharge = userRechargeMapper.findUserRechargeByOrderSn(outTradeNo);
        if (userRecharge == null) {
            log.error("【payCallback】支付回调=>订单不存在，订单号：{}", outTradeNo);
            response.getWriter().write("order not found");
            return;
        }

        // 检查订单状态，避免重复处理
        if (NumberUtils.INTEGER_ONE.equals(userRecharge.getOrderStatus())) {
            log.info("【payCallback】支付回调=>订单已处理，订单号：{}", outTradeNo);
            response.getWriter().write("success");
            return;
        }

        // 更新订单状态
        // 完成状态
        if (status == 3) {
            // 设置为成功
            userRecharge.setOrderStatus(1);
            userRecharge.setPayTime(new Date());
            userRecharge.setPaySn(payOrderId);
            // TODO 待确认 - 实际金额有可能扣除了手续费
            userRecharge.setPayAmt(new BigDecimal(amount));

            log.info("【payCallback】支付回调=>开始修改订单状态，订单号：{}", outTradeNo);
            int updateCount = userRechargeMapper.updateByPrimaryKeySelective(userRecharge);

            if (updateCount > 0) {
                // 更新用户余额
                User user = userMapper.selectByPrimaryKey(userRecharge.getUserId());
                if (user != null) {
                    BigDecimal totalAmt = user.getUserAmt().add(userRecharge.getPayAmt());
                    user.setUserAmt(totalAmt);
                    BigDecimal totalEnable = user.getEnableAmt().add(userRecharge.getPayAmt());
                    user.setEnableAmt(totalEnable);
                    int updateUserCount = userMapper.updateByPrimaryKeySelective(user);
                    if (updateUserCount > 0) {
                        log.info("【payCallback】支付回调=>用户余额更新成功，订单号：{}，用户ID：{}", outTradeNo, userRecharge.getUserId());
                    }
                }
            }
        } else if (status == 4) { // 失败状态
            userRecharge.setOrderStatus(2); // 设置为失败
            userRechargeMapper.updateByPrimaryKeySelective(userRecharge);
        }

        // 返回成功
        response.getWriter().write("success");
    }

    /**
     * 生成签名
     *
     * @param params 参数Map
     * @return 签名字符串
     */
    private String generateSign(Map<String, Object> params) {
        // 按照参数名ASCII码从小到大排序
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> entry : new TreeMap<>(params).entrySet()) {
            if (entry.getValue() != null && !"".equals(entry.getValue()) && !"sign".equals(entry.getKey())) {
                sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
        }

        // 去掉最后一个&
        String signStr = sb.toString();
        if (signStr.endsWith("&")) {
            signStr = signStr.substring(0, signStr.length() - 1);
        }

        // 拼接app_key
        signStr += appKey;

        // MD5加密
        return SecureUtil.md5(signStr);
    }
}
