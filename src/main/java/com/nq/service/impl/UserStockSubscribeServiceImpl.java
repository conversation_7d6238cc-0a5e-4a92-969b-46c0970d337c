package com.nq.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.dao.StockSubscribeMapper;
import com.nq.dao.UserMapper;
import com.nq.dao.UserPositionMapper;
import com.nq.dao.UserStockSubscribeMapper;
import com.nq.enums.ConsumptionTypeEnum;
import com.nq.enums.FundSourceTypeEnum;
import com.nq.pojo.*;
import com.nq.service.*;
import com.nq.utils.DateTimeUtil;
import com.nq.utils.KeyUtils;
import com.nq.utils.PropertiesUtil;
import com.nq.utils.UserInfoUtil;
import com.nq.utils.redis.JsonUtil;
import com.nq.utils.redis.RedisShardedPoolUtils;
import com.nq.utils.stock.BuyAndSellUtils;
import com.nq.utils.stock.GeneratePosition;
import com.nq.utils.stock.sina.SinaStockApi;
import com.nq.vo.stock.StockListVO;
import com.nq.vo.stock.UserStockSubscribeVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 新股申购
 *
 * <AUTHOR>
 * @date 2020/07/24
 */
@Service("IUserStockSubscribeService")
@Slf4j
public class UserStockSubscribeServiceImpl implements IUserStockSubscribeService {

    @Resource
    private UserStockSubscribeMapper userStockSubscribeMapper;

    @Autowired
    UserMapper userMapper;

    @Autowired
    ISiteMessageService iSiteMessageService;
    @Autowired
    StockSubscribeMapper stockSubscribeMapper;
    @Autowired
    IUserPositionService iUserPositionService;
    @Autowired
    ISiteProductService iSiteProductService;
    @Autowired
    IUserService iUserService;
    @Autowired
    IStockService iStockService;
    @Autowired
    ISiteSettingService iSiteSettingService;
    @Autowired
    ISiteSpreadService iSiteSpreadService;
    @Autowired
    UserPositionMapper userPositionMapper;
    @Autowired
    IAgentAgencyFeeService iAgentAgencyFeeService;

    @Autowired
    IUserFundSourceService iUserFundSourceService;

    /**
     * 用户新股申购
     *
     * @param model
     * @return
     */
    @Override
    public ServerResponse insert(UserStockSubscribe model, HttpServletRequest request) throws Exception {
        int ret = 0;
        if (model == null) {
            return ServerResponse.createByErrorMsg("参数错误");
        }
        String property = PropertiesUtil.getProperty("user.cookie.name");
        String header = request.getHeader(property);
        if (header != null) {
            String userJson = RedisShardedPoolUtils.get(header);
            User user = this.iUserService.getCurrentRefreshUser(request);
            if (user == null) {
                return ServerResponse.createBySuccessMsg("請先登錄");
            }
            if (model.getNewCode() != null) {
                StockSubscribe stockSubscribe =
                        stockSubscribeMapper.selectOne(new QueryWrapper<StockSubscribe>().eq("code", model.getNewCode()));
                // 实名认证开关
                SiteProduct siteProduct = iSiteProductService.getProductSetting();
                if (siteProduct.getRealNameDisplay()
                        && (StringUtils.isBlank(user.getRealName()) || StringUtils.isBlank(user.getIdCard()))) {
                    return ServerResponse.createByErrorMsg("下单失败，请先实名认证");
                }
                // 判断休息日不能买入
                if (siteProduct.getHolidayDisplay()) {
                    return ServerResponse.createByErrorMsg("周末或节假日不能交易！");
                }
                // // 重复申购限制 -- 20240729 要求去除重复限制
                // UserStockSubscribe userStockSubscribe =
                // userStockSubscribeMapper.selectOne(new QueryWrapper<UserStockSubscribe>()
                // .eq("new_code", model.getNewCode()).eq("user_id", user.getId()));
                // if (userStockSubscribe != null) {
                // return ServerResponse.createByErrorMsg("请勿重复申购");
                // }

                if (siteProduct.getRealNameDisplay() && user.getIsLock().intValue() == 1) {
                    return ServerResponse.createByErrorMsg("下单失败，账户已被锁定");
                }
                if (stockSubscribe == null) {
                    return ServerResponse.createByErrorMsg("新股代码不存在");
                }
                // 时间判定当前时间是否是申购时间
                SiteSetting siteSetting = this.iSiteSettingService.getSiteSetting();
                if (siteSetting == null) {
                    log.error("下单出错，网站设置表不存在");
                    return ServerResponse.createByErrorMsg("下单失败，系统设置错误");
                }
                String am_begin = siteSetting.getTransAmBegin();
                String am_end = siteSetting.getTransAmEnd();
                String pm_begin = siteSetting.getTransPmBegin();
                String pm_end = siteSetting.getTransPmEnd();
                boolean am_flag = BuyAndSellUtils.isTransTime(am_begin, am_end);
                boolean pm_flag = BuyAndSellUtils.isTransTime(pm_begin, pm_end);
                log.info("是否在上午交易时间 = {} 是否在下午交易时间 = {}", Boolean.valueOf(am_flag), Boolean.valueOf(pm_flag));
                // if (!am_flag && !pm_flag) {
                // return ServerResponse.createByErrorMsg("申购失败，不在交易时段内");
                // }

                if (model.getApplyNums() == null || model.getApplyNums() > stockSubscribe.getOrderNumber() * 10000) {
                    return ServerResponse.createByErrorMsg("购买数量异常或大于发行数量" + stockSubscribe.getOrderNumber() * 10000);
                }

                // 优先取折扣价格，如果为空则取发行价
                BigDecimal buyPrice =
                        Optional.ofNullable(stockSubscribe.getDiscountedPrice()).orElse(stockSubscribe.getPrice());

                //判断余额是否等于0
                if (user.getEnableAmt().compareTo(BigDecimal.ZERO) == 0 || user.getEnableAmt()
                        .compareTo(new BigDecimal(model.getApplyNums()).multiply(buyPrice)) < 0) {
                    return ServerResponse.createByErrorMsg("用户可用余额不足，申购条件不满足");
                }
                user.setEnableAmt(user.getEnableAmt()
                        .subtract(new BigDecimal(model.getApplyNums()).multiply(buyPrice)));
                if (user.getDjzj() != null) {
                    user.setDjzj(user.getDjzj()
                            .add(new BigDecimal(model.getApplyNums()).multiply(buyPrice)));
                } else {
                    user.setDjzj(new BigDecimal(model.getApplyNums()).multiply(buyPrice));
                }
                int u = userMapper.updateById(user);
                if (u <= 0) {
                    return ServerResponse.createByErrorMsg("未知原因，申购失败");
                }

                // 消费用户资金来源（扫款申购）
                BigDecimal subscribeAmount = new BigDecimal(model.getApplyNums()).multiply(stockSubscribe.getPrice());
                if (model.getType() == 1) {
                    ServerResponse fundSourceResponse = iUserFundSourceService.consumeFunds(
                            user.getId(),
                            subscribeAmount,
                            ConsumptionTypeEnum.STOCK_SUBSCRIBE.getCode(),
                            "STOCK_SUBSCRIBE_" + model.getOrderNo(),
                            "新股申购，新股代码：" + stockSubscribe.getCode() + "，申购数量：" + model.getApplyNums() + "股");
                } else {
                    //大宗交易
                    ServerResponse fundSourceResponse = iUserFundSourceService.consumeFunds(
                            user.getId(),
                            subscribeAmount,
                            ConsumptionTypeEnum.BULK_TRADE.getCode(),
                            "BULK_TRADE_" + model.getOrderNo(),
                            "大宗交易，新股代码：" + stockSubscribe.getCode() + "，申购数量：" + model.getApplyNums() + "股");
                }


                model.setUserId(user.getId());
                model.setNewName(stockSubscribe.getName());
                model.setAgentId(user.getAgentId());
                model.setAgentName(user.getAgentName());
                model.setNewType(stockSubscribe.getStockType());
                model.setPhone(user.getPhone());
                model.setBuyPrice(buyPrice);

                model.setBond(new BigDecimal(model.getApplyNums()).multiply(buyPrice));
                model.setRealName(Objects.equals(user.getRealName(), "") || user.getRealName() == null ? "模拟用户无实名"
                        : user.getRealName());
                model.setAddTime(new Date());
                model.setOrderNo(KeyUtils.getUniqueKey());
                model.setType(model.getType());
            }

            ret = userStockSubscribeMapper.insert(model);
            if (ret > 0) {
                return ServerResponse.createBySuccessMsg("申购成功");
            } else {
                return ServerResponse.createByErrorMsg("申购失败");
            }
        }
        return ServerResponse.createByErrorMsg("未登录");
    }

    @Override
    public int update(UserStockSubscribe model) {
        int ret = userStockSubscribeMapper.update1(model);
        return ret > 0 ? ret : 0;
    }

    /**
     * admin 新股申购-添加和修改
     */
    @Override
    public ServerResponse save(UserStockSubscribe model, HttpServletRequest request) {

        if (model.getId() != null) {

            UserStockSubscribe userStockSubscribe = userStockSubscribeMapper.load(model.getId());
            User user = userMapper.selectByPrimaryKey(userStockSubscribe.getUserId());
            // 已经转持仓了不能修改
            if (userStockSubscribe.getStatus() == 5) {
                return ServerResponse.createByErrorMsg("已经转持仓不能修改！");
            }
            log.info(JSONUtil.toJsonStr(model));
            if (model.getStatus() == 5) {
                if (userStockSubscribe.getStatus() != 4) {
                    return ServerResponse.createByErrorMsg("未缴纳中签资金无法转持仓！");
                }
                int res = userStockSubscribeMapper.update1(model);
                if (res > 0) {
                    return iUserPositionService.newStockToPosition(model.getId());
                } else {
                    return ServerResponse.createByErrorMsg("未知原因，审核失败，数据库更新失败请联系技术人员！");
                }

            }

            // 未中签
            if (model.getStatus() == 2) {
                model.setEndTime(DateTimeUtil.getCurrentDate());
                int res = userStockSubscribeMapper.update1(model);

                BigDecimal refundAmount = userStockSubscribe.getBond();
                if (refundAmount != null && refundAmount.compareTo(BigDecimal.ZERO) > 0) {
                    // 更新用户资金
                    user.setEnableAmt(user.getEnableAmt().add(refundAmount));
                    if (user.getDjzj() != null && user.getDjzj().compareTo(refundAmount) >= 0) {
                        user.setDjzj(user.getDjzj().subtract(refundAmount));
                    } else {
                        user.setDjzj(BigDecimal.ZERO);
                    }

                    int updateUserResult = userMapper.updateByPrimaryKeySelective(user);
                    if (updateUserResult > 0) {
                        if (model.getType() == 2) {
                            // 添加资金来源记录（新股未中签返还）
                            ServerResponse fundSourceResponse = iUserFundSourceService.addFundSource(
                                    user.getId(),
                                    FundSourceTypeEnum.STOCK_SUBSCRIBE_REFUND,
                                    refundAmount,
                                    "SUBSCRIBE_REFUND_" + userStockSubscribe.getId(),
                                    "新股申购未中签返还,新股代码：" + userStockSubscribe.getNewCode() + "，申购数量：" + userStockSubscribe.getApplyNums() + "股");
                        } else {
                            //大宗交易
                            ServerResponse fundSourceResponse = iUserFundSourceService.consumeFunds(
                                    user.getId(),
                                    refundAmount,
                                    ConsumptionTypeEnum.BULK_TRADE.getCode(),
                                    "BULK_TRADE_" + model.getOrderNo(),
                                    "大宗交易，新股代码：" + userStockSubscribe.getNewCode() + "，申购数量：" + model.getApplyNums() + "股");
                        }

                    } else {
                        log.error("新股未中签返还资金失败");
                    }
                }


                SiteMessage siteMessage = new SiteMessage();
                siteMessage.setUserId(userStockSubscribe.getUserId());
                siteMessage.setUserName(userStockSubscribe.getRealName());
                siteMessage.setTypeName("新股申购");
                siteMessage.setStatus(1);
                if (model.getType() == 1) {
                    siteMessage.setContent("【新股申购未中签】很遗憾，您的新股申购本次未中签，申购金额：" + userStockSubscribe.getBond() + "已退还。");
                } else {
                    siteMessage.setContent("【大宗交易申购未中签】很遗憾，您的新股申购本次未中签，申购金额：" + userStockSubscribe.getBond() + "已退还。");
                }
                siteMessage.setAddTime(DateTimeUtil.getCurrentDate());
                iSiteMessageService.insert(siteMessage);

                if (res > 0) {
                    return ServerResponse.createBySuccessMsg("操作成功！");
                } else {
                    return ServerResponse.createByErrorMsg("未知原因，审核失败，数据库更新失败请联系技术人员！");
                }
            }

            // 中签
            if (model.getStatus() == 3) {
                if (model.getApplyNumber() == 0) {
                    return ServerResponse.createByErrorMsg("中签数不能为0！");
                }
                //申购时已扣款，中签时则为已缴纳状态
                model.setStatus(4);

                model.setEndTime(DateTimeUtil.getCurrentDate());
                // 缴纳金额 = 中签数 * 新股价格
                // BigDecimal payPrice =
                // userStockSubscribe.getBuyPrice().multiply(BigDecimal.valueOf(model.getApplyNumber()));
                // model.setBond(payPrice);
                int res = userStockSubscribeMapper.update1(model);
                //中签数量小于申购数量，退回未中签的申购金额
                if (model.getApplyNumber() < userStockSubscribe.getApplyNums()) {
                    // 未中签数量
                    int notWinNum = userStockSubscribe.getApplyNums() - model.getApplyNumber();
                    BigDecimal refundAmount = userStockSubscribe.getBuyPrice().multiply(
                            new BigDecimal(notWinNum));
                    user.setEnableAmt(user.getEnableAmt().add(refundAmount));
                    if (user.getDjzj() != null && user.getDjzj().compareTo(refundAmount) >= 0) {
                        user.setDjzj(user.getDjzj().subtract(refundAmount));
                    } else {
                        user.setDjzj(BigDecimal.ZERO);
                    }

                    int updateUserResult = userMapper.updateByPrimaryKeySelective(user);
                    if (updateUserResult > 0) {
                        if (model.getType() == 1) {
                            // 添加资金来源记录（新股未中签返还）
                            ServerResponse fundSourceResponse = iUserFundSourceService.addFundSource(
                                    user.getId(),
                                    FundSourceTypeEnum.STOCK_SUBSCRIBE_REFUND,
                                    refundAmount,
                                    "SUBSCRIBE_REFUND_" + userStockSubscribe.getId(),
                                    "新股申购未中签返还，新股代码：" + userStockSubscribe.getNewCode() + "，未中签数量：" + notWinNum + "股");
                        } else {
                            //大宗交易
                            ServerResponse fundSourceResponse = iUserFundSourceService.consumeFunds(
                                    user.getId(),
                                    refundAmount,
                                    ConsumptionTypeEnum.BULK_TRADE.getCode(),
                                    "BULK_TRADE_" + model.getOrderNo(),
                                    "大宗交易，新股代码：" + userStockSubscribe.getNewCode() + "，未中签数量：" + notWinNum + "股");
                        }

                    }
                }

                SiteMessage siteMessage = new SiteMessage();
                siteMessage.setUserId(userStockSubscribe.getUserId());
                siteMessage.setUserName(userStockSubscribe.getRealName());
                siteMessage.setTypeName("新股申购");
                siteMessage.setStatus(1);
                if (model.getType() == 1) {
                    siteMessage.setContent("【新股申购中签】恭喜您，新股申购中签成功，请及时关注哦。");
                } else {
                    siteMessage.setContent("【大宗交易申购中签】恭喜您，新股申购中签成功，请及时关注哦。");
                }
                siteMessage.setAddTime(DateTimeUtil.getCurrentDate());
                iSiteMessageService.insert(siteMessage);

                if (res > 0) {
                    return ServerResponse.createBySuccessMsg("操作成功！");
                } else {
                    return ServerResponse.createByErrorMsg("未知原因，审核失败，数据库更新失败请联系技术人员！");
                }
            }

            // 缴纳资金
            if (model.getStatus() == 4) {
                if (model.getApplyNumber() == 0) {
                    return ServerResponse.createByErrorMsg("中签数不能为0！");
                }
                // 未中签状态不能进行缴纳
                if (userStockSubscribe.getStatus() != 3) {
                    return ServerResponse.createByErrorMsg("未中签不能缴纳中签资金！");
                }
                //判断是否已经时缴纳状态
                if (userStockSubscribe.getStatus() == 4) {
                    return ServerResponse.createByErrorMsg("已经缴纳中签资金不能重复缴纳！");
                }

                // 缴纳金额 = 中签数 * 新股价格
                BigDecimal payPrice =
                        userStockSubscribe.getBuyPrice().multiply(BigDecimal.valueOf(model.getApplyNumber()));

                if (user.getEnableAmt().compareTo(payPrice) < 0) {
                    return ServerResponse
                            .createByErrorMsg("缴纳资金不足！当前可用资金：" + user.getEnableAmt() + "所需缴纳资金：" + payPrice);
                }

                user.setEnableAmt(user.getEnableAmt().subtract(payPrice));

                int ret1 = userMapper.updateByPrimaryKey(user);
                if (ret1 <= 0) {
                    return ServerResponse.createByErrorMsg("未知原因，申购失败,数据库更新失败请联系技术人员！");
                }

                // 消费用户资金来源（新股认缴）
                ServerResponse fundSourceResponse = iUserFundSourceService.consumeFunds(
                        user.getId(),
                        payPrice);

                if (!fundSourceResponse.isSuccess()) {
                    log.error("新股认缴消费用户资金来源失败: {}", fundSourceResponse.getMsg());
                } else {
                    log.info("新股认缴消费用户资金来源成功");
                }

                // 给用户推送消息
                SiteMessage siteMessage = new SiteMessage();
                siteMessage.setUserId(userStockSubscribe.getUserId());
                siteMessage.setUserName(userStockSubscribe.getRealName());
                siteMessage.setTypeName("新股申购");
                siteMessage.setStatus(1);

                siteMessage.setAddTime(DateTimeUtil.getCurrentDate());
                siteMessage.setContent("【新股申购中签】恭喜您，新股申购中签成功，中签金额：" + payPrice + "，请及时关注哦。");
                iSiteMessageService.insert(siteMessage);

                int res = userStockSubscribeMapper.update1(model);
                if (res > 0) {
                    return ServerResponse.createBySuccessMsg("操作成功！");
                } else {
                    return ServerResponse.createByErrorMsg("未知原因，审核失败，数据库更新失败请联系技术人员！");
                }
            }

        }
        return ServerResponse.createByErrorMsg("操作失败");
    }

    /**
     * 发送站内信
     */
    @Override
    public ServerResponse sendMsg(UserStockSubscribe model, HttpServletRequest request) {
        int ret = 0;

        if (model != null) {
            // 所有人发站内信
            if (model.getUserId() == 0) {
                List<User> users = this.userMapper.listByAdmin(null, null, null, null, null);
                for (int k = 0; k < users.size(); k++) {
                    User user = users.get(k);
                    SiteMessage siteMessage = new SiteMessage();
                    siteMessage.setUserId(user.getId());
                    siteMessage.setUserName(user.getRealName());
                    siteMessage.setTypeName("站内消息");
                    siteMessage.setStatus(1);
                    siteMessage.setContent("【站内消息】" + model.getRemarks());
                    siteMessage.setAddTime(DateTimeUtil.getCurrentDate());
                    ret = iSiteMessageService.insert(siteMessage);
                }
            } else {
                // 指定用户发站内信
                User user = userMapper.selectByPrimaryKey(model.getUserId());
                SiteMessage siteMessage = new SiteMessage();
                siteMessage.setUserId(user.getId());
                siteMessage.setUserName(user.getRealName());
                siteMessage.setTypeName("站内消息");
                siteMessage.setStatus(1);
                siteMessage.setContent("【站内消息】" + model.getRemarks());
                siteMessage.setAddTime(DateTimeUtil.getCurrentDate());
                ret = iSiteMessageService.insert(siteMessage);
            }
        }
        if (ret > 0) {
            return ServerResponse.createBySuccessMsg("操作成功");
        }
        return ServerResponse.createByErrorMsg("操作失败");
    }

    /*新股申购-查询列表*/
    @Override
    public ServerResponse<PageInfo> getList(int pageNum, int pageSize, String keyword, HttpServletRequest request) {
        PageHelper.startPage(pageNum, pageSize);
        List<UserStockSubscribe> listData = this.userStockSubscribeMapper.pageList(pageNum, pageSize, keyword);
        PageInfo pageInfo = new PageInfo(listData);
        pageInfo.setList(listData);
        return ServerResponse.createBySuccess(pageInfo);
    }

    /*新股申购-查询详情*/
    @Override
    public ServerResponse getDetail(int id) {
        return ServerResponse.createBySuccess(this.userStockSubscribeMapper.load(id));
    }

    /*新股申购-查询用户最新新股申购数据*/
    @Override
    public ServerResponse getOneSubscribeByUserId(String type, HttpServletRequest request) {
        String property = PropertiesUtil.getProperty("user.cookie.name");
        String header = request.getHeader(property);
        if (header != null) {
            String userJson = RedisShardedPoolUtils.get(header);
            User user = (User) JsonUtil.string2Obj(userJson, User.class);
            if (user == null) {
                return ServerResponse.createByErrorMsg("用户未登录");
            }
            // List<UserStockSubscribe> userStockSubscribeList = null;
            // if (type == null || type.equals("")) {
            // userStockSubscribeList = this.userStockSubscribeMapper.selectList(
            // new QueryWrapper<>(new UserStockSubscribe()).eq("phone", user.getPhone()).orderByDesc("add_time"));
            // } else {
            // userStockSubscribeList =
            // this.userStockSubscribeMapper.selectList(new QueryWrapper<>(new UserStockSubscribe())
            // .eq("phone", user.getPhone()).eq("type", type).orderByDesc("add_time"));
            // }

            List<UserStockSubscribe> userStockSubscribeList = this.userStockSubscribeMapper.selectList(
                    new LambdaQueryWrapper<UserStockSubscribe>().eq(UserStockSubscribe::getPhone, user.getPhone())
                            .eq(StrUtil.isNotBlank(type), UserStockSubscribe::getType, type)
                            // 1 待中签、2 未中签
                            .and(i -> i.eq(UserStockSubscribe::getStatus, 1).or().eq(UserStockSubscribe::getStatus, 2))
                            .orderByDesc(UserStockSubscribe::getAddTime));

            List<UserStockSubscribe> list = new ArrayList<>();
            for (UserStockSubscribe userStockSubscribe : userStockSubscribeList) {
                StockSubscribe stockSubscribe = stockSubscribeMapper
                        .selectOne(new QueryWrapper<>(new StockSubscribe()).eq("code", userStockSubscribe.getNewCode()));
                if (stockSubscribe != null) {
                    // 中签数量不为空，则bond=购买价格*中签数量
                    if (Objects.nonNull(userStockSubscribe.getApplyNumber())) {
                        userStockSubscribe.setBond(userStockSubscribe.getBuyPrice()
                                .multiply(BigDecimal.valueOf(userStockSubscribe.getApplyNumber())));
                    }
                    list.add(userStockSubscribe);
                }
            }

            // PageInfo pageInfo = new PageInfo();
            // pageInfo.setList(userStockSubscribe);
            // GoogleTranslateUtil transan = new GoogleTranslateUtil();
            // //list转String
            // String json = JsonUtil.obj2String(list);
            // String translate;
            // try {
            // translate = transan.translate("zh", "en", json);
            // } catch (Exception e) {
            // throw new RuntimeException(e);
            // }
            // //String转list
            // List<UserStockSubscribe> list1 = JsonUtil.string2Obj(translate, List.class, UserStockSubscribe.class);
            return ServerResponse.createBySuccess(list);
        }
        return ServerResponse.createByErrorMsg("请先登录");
    }

    /**
     * 新股申购-用户提交金额
     */
    @Override
    @Transactional
    public ServerResponse userSubmit(Integer id, HttpServletRequest request) {
        int ret = 0;
        String property = PropertiesUtil.getProperty("user.cookie.name");
        String header = request.getHeader(property);
        if (header != null) {
            String userJson = RedisShardedPoolUtils.get(header);
            User user = (User) JsonUtil.string2Obj(userJson, User.class);
            if (user == null) {
                return ServerResponse.createByErrorMsg("用户未登录");
            }
            if (id == null) {
                return ServerResponse.createByErrorMsg("参数错误");
            }
            UserStockSubscribe userStockSubscribe = userStockSubscribeMapper.load(id);
            log.info("userStockSubscribe:{}", userStockSubscribe);
            if (userStockSubscribe != null && userStockSubscribe.getUserId().equals(user.getId())) {
                StockSubscribe stockSubscribe = stockSubscribeMapper
                        .selectOne(new QueryWrapper<>(new StockSubscribe()).eq("code", userStockSubscribe.getNewCode()));
                if (userStockSubscribe.getType() == 2) {
                    return ServerResponse.createByErrorMsg("线下配售无需支付");
                }
                // 判断时间 2024-08-07 要求：新股没上市之前都可以认缴
                // if (stockSubscribe.getSubscriptionTime() == null
                // || stockSubscribe.getSubscriptionTime().getTime() < DateTimeUtil.getCurrentDate().getTime()) {
                // return ServerResponse.createByErrorMsg("不在认缴时间");
                // }
                // 上市日期
                if (Objects.nonNull(stockSubscribe.getListDate())
                        && stockSubscribe.getListDate().getTime() < DateTimeUtil.getCurrentDate().getTime()) {
                    return ServerResponse.createByErrorMsg("不在认缴时间");
                }
                if (userStockSubscribe.getStatus() == 3) {
                    userStockSubscribe.setSubmitTime(DateTimeUtil.getCurrentDate());
                    userStockSubscribe.setStatus(4);
                    User user1 = userMapper.selectByPrimaryKey(userStockSubscribe.getUserId());
                    // log.info("user" + user1);
                    BigDecimal payAmt =
                            userStockSubscribe.getBuyPrice().multiply(new BigDecimal(userStockSubscribe.getApplyNumber()));
                    if (user1.getEnableAmt().compareTo(payAmt) < 0) {
                        return ServerResponse.createByErrorMsg("余额不足");
                    }
                    // log.info("原可用资金"+user1.getEnableAmt());
                    BigDecimal enableAmt = user1.getEnableAmt().subtract(payAmt);
                    // log.info("enableAmt" + enableAmt);
                    user1.setEnableAmt(enableAmt);
                    // log.info("可用资金" +
                    // user1.getEnableAmt()+"保证金"+userStockSubscribe.getBond()+"原djzj"+user1.getDjzj());
                    if (user1.getDjzj() != null) {
                        user1.setDjzj(user1.getDjzj().add(payAmt));
                    } else {
                        user1.setDjzj(payAmt);
                    }
                    ret = userMapper.updateByPrimaryKeySelective(user1);

                    if (ret > 0) {
                        // 消费用户资金来源
                        ServerResponse fundSourceResponse = iUserFundSourceService.consumeFunds(
                                user1.getId(),
                                payAmt);

                        if (!fundSourceResponse.isSuccess()) {
                            log.error("新股认缴消费用户资金来源失败: {}", fundSourceResponse.getMsg());
                        } else {
                            log.info("新股认缴消费用户资金来源成功");
                        }
                    }
                } else {
                    return ServerResponse.createByErrorMsg("未中签无需缴费");
                }
            } else {
                return ServerResponse.createByErrorMsg("新股申购订单不存在！");
            }

            if (ret > 0) {
                ret = userStockSubscribeMapper.update1(userStockSubscribe);
                if (ret > 0) {
                    return ServerResponse.createBySuccessMsg("操作成功");
                } else {
                    return ServerResponse.createByErrorMsg("操作失败");
                }
            } else {
                return ServerResponse.createByErrorMsg("扣款失败");
            }
        }
        return ServerResponse.createByErrorMsg("请先登录");
    }

    /**
     * 新股申购-删除
     *
     * @param id
     * @param request
     * @return
     */
    @Override
    public ServerResponse del(int id, HttpServletRequest request) {
        int ret = 0;
        if (id > 0) {
            ret = userStockSubscribeMapper.delete1(id);
        }
        if (ret > 0) {
            return ServerResponse.createBySuccessMsg("操作成功");
        }
        return ServerResponse.createByErrorMsg("操作失败");
    }

    /**
     * 新股抢筹 下单
     */
    @Override
    public ServerResponse buyNewStockQc(String code, Integer num, HttpServletRequest request) {
        User user = this.iUserService.getCurrentRefreshUser(request);

        if (code == null || "".equals(code) || num == null || num <= 0) {
            return ServerResponse.createByErrorMsg("股票代码不能为空或者购买数量异常");
        }
        StockSubscribe stockSubscribe =
                stockSubscribeMapper.selectOne(new QueryWrapper<StockSubscribe>().eq("code", code));
        // 实名认证开关
        SiteProduct siteProduct = iSiteProductService.getProductSetting();
        if (siteProduct.getRealNameDisplay()
                && (StringUtils.isBlank(user.getRealName()) || StringUtils.isBlank(user.getIdCard()))) {
            return ServerResponse.createByErrorMsg("下单失败，请先实名认证");
        }
        // 判断休息日不能买入
        if (siteProduct.getHolidayDisplay()) {
            return ServerResponse.createByErrorMsg("周末或节假日不能交易！");
        }
        // 重复申购限制
        // UserStockSubscribe userStockSubscribe = userStockSubscribeMapper.selectOne(new
        // QueryWrapper<UserStockSubscribe>().eq("new_code", model.getNewCode()).eq("user_id", user.getId()));
        // if (userStockSubscribe != null) {
        // return ServerResponse.createByErrorMsg("请勿重复申购");
        // }
        if (siteProduct.getRealNameDisplay() && user.getIsLock().intValue() == 1) {
            return ServerResponse.createByErrorMsg("下单失败，账户已被锁定");
        }
        if (stockSubscribe == null) {
            return ServerResponse.createByErrorMsg("新股代码不存在");
        }

        if ("sh".equals(stockSubscribe.getStockType()) || "sh".equals(stockSubscribe.getStockType()) && num < 1000) {
            return ServerResponse.createByErrorMsg("沪市新股申购数量最小为1000股");
        } else if ("sz".equals(stockSubscribe.getStockType())
                || "sz".equals(stockSubscribe.getStockType()) && num < 500) {
            return ServerResponse.createByErrorMsg("深市新股申购数量最小为500股");
        }

        if (user.getEnableAmt().compareTo(new BigDecimal(num).multiply(stockSubscribe.getPrice())) < 0) {
            return ServerResponse.createByErrorMsg("用户可用余额不足，申购条件不满足");
        }
        user.setEnableAmt(user.getEnableAmt().subtract(new BigDecimal(num).multiply(stockSubscribe.getPrice())));
        // if (user.getDjzj()!=null) {
        // user.setDjzj(user.getDjzj().add(new BigDecimal(model.getApplyNums()).multiply(stockSubscribe.getPrice())));
        // }else
        // {
        // user.setDjzj(new BigDecimal(model.getApplyNums()).multiply(stockSubscribe.getPrice()));
        // }
        int u = userMapper.updateById(user);
        if (u <= 0) {
            return ServerResponse.createByErrorMsg("未知原因，申购失败");
        }
        UserStockSubscribe userStockSubscribe = new UserStockSubscribe();
        userStockSubscribe.setUserId(user.getId());
        userStockSubscribe.setNewName(stockSubscribe.getName());
        userStockSubscribe.setAgentId(user.getAgentId());
        userStockSubscribe.setAgentName(user.getAgentName());
        userStockSubscribe.setPhone(user.getPhone());
        userStockSubscribe.setApplyNums(num);
        userStockSubscribe.setNewType(stockSubscribe.getStockType());
        userStockSubscribe.setBuyPrice(stockSubscribe.getPrice());
        userStockSubscribe.setNewCode(stockSubscribe.getCode());
        userStockSubscribe.setBond(new BigDecimal(num).multiply(stockSubscribe.getPrice()));
        userStockSubscribe.setRealName(
                Objects.equals(user.getRealName(), "") || user.getRealName() == null ? "模拟用户无实名" : user.getRealName());
        userStockSubscribe.setAddTime(new Date());
        userStockSubscribe.setOrderNo(KeyUtils.getUniqueKey());
        userStockSubscribe.setType(3);
        int ret = userStockSubscribeMapper.insert(userStockSubscribe);
        if (ret > 0) {

            return ServerResponse.createBySuccessMsg("申购抢筹成功");
        } else {
            return ServerResponse.createByErrorMsg("申购抢筹失败");
        }
    }

    /**
     * 新股抢筹 股票列表
     */
    @Override
    public ServerResponse getStockQcList(HttpServletRequest request) {
        String nowDate = DateTimeUtil.stampToDate(String.valueOf(System.currentTimeMillis()));
        List<StockSubscribe> stockSubscribeListQc =
                this.stockSubscribeMapper.selectList(new QueryWrapper<StockSubscribe>().eq("list_date", nowDate));
        return ServerResponse.createBySuccess(stockSubscribeListQc);
    }

    /**
     * 用户新股抢筹列表
     *
     * @param pageNum
     * @param pageSize
     * @param keyword
     * @param request
     * @return
     */
    @Override
    public ServerResponse getQcList(int pageNum, int pageSize, String keyword, HttpServletRequest request) {
        PageHelper.startPage(pageNum, pageSize);
        List<UserStockSubscribe> qcList;
        if (StringUtils.isNotEmpty(keyword)) {
            qcList = userStockSubscribeMapper
                    .selectList(new QueryWrapper<UserStockSubscribe>().like("phone", keyword).or().like("new_code", keyword)
                            .or().like("new_name", keyword).or().like("status", keyword).eq("type", 3).orderByDesc("add_time"));
        } else {
            qcList = userStockSubscribeMapper
                    .selectList(new QueryWrapper<UserStockSubscribe>().eq("type", 3).orderByDesc("add_time"));
        }
        PageInfo pageInfo = new PageInfo(qcList);
        pageInfo.setList(qcList);
        return ServerResponse.createBySuccess(pageInfo);
    }

    /**
     * 新股抢筹审核
     *
     * @param status
     * @param request
     * @return
     */
    @Override
    public ServerResponse updateQcByAdmin(String id, String status, String num, HttpServletRequest request) {
        if (StringUtils.isEmpty(id) || StringUtils.isEmpty(status)) {
            return ServerResponse.createByErrorMsg("参数错误");
        }
        UserStockSubscribe userStockSubscribe = userStockSubscribeMapper.selectById(id);
        if (userStockSubscribe == null) {
            return ServerResponse.createByErrorMsg("抢筹记录不存在");
        }
        User user = userMapper.selectById(userStockSubscribe.getUserId());
        if (user == null) {
            return ServerResponse.createByErrorMsg("用户不存在");
        }

        if (userStockSubscribe.getStatus() == 1) {
            if ("2".equals(status)) {
                user.setEnableAmt(user.getEnableAmt().add(userStockSubscribe.getBond()));
                int ret = userMapper.updateById(user);
                if (ret <= 0) {
                    return ServerResponse.createByErrorMsg("未知原因，审核失败");
                }
                userStockSubscribe.setStatus(2);
                userStockSubscribe.setEndTime(new Date());
                int ret1 = userStockSubscribeMapper.updateById(userStockSubscribe);
                if (ret1 > 0) {
                    return ServerResponse.createBySuccessMsg("审核成功");
                } else {
                    return ServerResponse.createByErrorMsg("审核失败");
                }
            } else if ("3".equals(status)) {
                if (StringUtils.isEmpty(num) || Integer.parseInt(num) <= 0
                        || Integer.parseInt(num) > userStockSubscribe.getApplyNums()) {
                    return ServerResponse.createByErrorMsg("中签数量不能为空，且不能大于申购数量");
                }
                Stock stock = (Stock) this.iStockService.findStockByCode(userStockSubscribe.getNewCode()).getData();
                if (stock == null) {
                    log.info("股票不存在");
                    return ServerResponse.createByErrorMsg("股票不存在");
                }
                SiteSetting siteSetting = this.iSiteSettingService.getSiteSetting();
                if (siteSetting == null) {
                    log.error("下单出错，网站设置表不存在");
                    return ServerResponse.createByErrorMsg("下单失败，系统设置错误");
                }
                BigDecimal buy_amt = (userStockSubscribe.getBuyPrice()).multiply(new BigDecimal(num));
                UserPosition userPosition = new UserPosition();
                userPosition.setPositionType(user.getAccountType());
                userPosition.setPositionSn(KeyUtils.getUniqueKey());
                userPosition.setUserId(user.getId());
                userPosition.setNickName(user.getRealName());
                userPosition.setAgentId(user.getAgentId());
                userPosition.setStockCode(stock.getStockCode());
                userPosition.setStockName(stock.getStockName());
                userPosition.setStockGid(stock.getStockGid());
                userPosition.setStockSpell(stock.getStockSpell());
                userPosition.setBuyOrderId(GeneratePosition.getPositionId());
                userPosition.setBuyOrderTime(new Date());
                userPosition.setBuyOrderPrice(userStockSubscribe.getBuyPrice());
                userPosition.setOrderDirection("买涨");
                userPosition.setOrderNum(Integer.valueOf(num));

                if (stock.getStockPlate() != null) {
                    userPosition.setStockPlate(stock.getStockPlate());
                }
                userPosition.setIsLock(Integer.valueOf(0));
                userPosition.setOrderLever(1);
                userPosition.setOrderTotalPrice(buy_amt);

                // 递延费特殊处理
                BigDecimal stayFee = userPosition.getOrderTotalPrice().multiply(siteSetting.getStayFee());
                BigDecimal allStayFee = stayFee.multiply(new BigDecimal(1));
                userPosition.setOrderStayFee(allStayFee);
                userPosition.setOrderStayDays(1);

                BigDecimal buy_fee_amt = buy_amt.multiply(siteSetting.getBuyFee()).setScale(2, 4);
                log.info("创建模拟持仓 手续费（配资后总资金 * 百分比） = {}", buy_fee_amt);
                userPosition.setOrderFee(buy_fee_amt);

                BigDecimal buy_yhs_amt = buy_amt.multiply(siteSetting.getDutyFee()).setScale(2, 4);
                log.info("创建模拟持仓 印花税（配资后总资金 * 百分比） = {}", buy_yhs_amt);
                userPosition.setOrderSpread(buy_yhs_amt);
                StockListVO stockListVO =
                        SinaStockApi.assembleStockListVO(SinaStockApi.getSinaStock(stock.getStockGid()));
                BigDecimal now_price = new BigDecimal(stockListVO.getNowPrice());

                // if (now_price.compareTo(new BigDecimal("0")) == 0) {
                // log.info(stock.getStockGid()+"报价0，");
                // return ServerResponse.createByErrorMsg("报价0，请稍后再试");
                //
                // }

                // double stock_crease = stockListVO.getHcrate().doubleValue();
                // SiteSpread siteSpread = iSiteSpreadService.findSpreadRateOne(new BigDecimal(stock_crease), buy_amt,
                // stock.getStockCode(), now_price);
                BigDecimal spread_rate_amt = new BigDecimal("0");
                // if(siteSpread != null){
                // spread_rate_amt = buy_amt.multiply(siteSpread.getSpreadRate()).setScale(2, 4);
                // log.info("用户购买点差费（配资后总资金 * 百分比{}） = {}", siteSpread.getSpreadRate(), spread_rate_amt);
                // } else{
                // log.info("用户购买点差费（配资后总资金 * 百分比{}） = {}", "设置异常", spread_rate_amt);
                // }

                userPosition.setSpreadRatePrice(spread_rate_amt);

                BigDecimal profit_and_lose = new BigDecimal("0");
                userPosition.setProfitAndLose(profit_and_lose);

                BigDecimal all_profit_and_lose =
                        profit_and_lose.subtract(buy_fee_amt).subtract(buy_yhs_amt).subtract(spread_rate_amt);
                userPosition.setAllProfitAndLose(all_profit_and_lose);

                userPosition.setOrderStayDays(Integer.valueOf(0));
                userPosition.setOrderStayFee(new BigDecimal("0"));
                userPosition.setSpreadRatePrice(new BigDecimal("0"));

                int insertPositionCount = this.userPositionMapper.insert(userPosition);
                if (insertPositionCount > 0) {
                    log.info("【抢筹创建持仓】保存记录成功");
                } else {
                    log.error("【抢筹创建持仓】保存记录出错");
                }
                iAgentAgencyFeeService.AgencyFeeIncome(1, userPosition.getPositionSn());
                userStockSubscribe.setStatus(3);
                userStockSubscribe.setEndTime(new Date());
                userStockSubscribe.setFixTime(new Date());
                userStockSubscribe.setApplyNumber(Integer.valueOf(num));
                this.userStockSubscribeMapper.updateById(userStockSubscribe);
                BigDecimal reimburse = new BigDecimal(userStockSubscribe.getApplyNums() - Integer.parseInt(num))
                        .multiply(userStockSubscribe.getBuyPrice());
                user.setEnableAmt(user.getEnableAmt().add(reimburse));
                int ret = userMapper.updateById(user);
                if (ret <= 0) {
                    return ServerResponse.createByErrorMsg("未知原因，审核失败");
                } else {
                    // 添加资金来源记录（部分中签返还多余资金）
                    if (reimburse.compareTo(BigDecimal.ZERO) > 0) {
                        ServerResponse fundSourceResponse = iUserFundSourceService.addFundSource(
                                user.getId(),
                                FundSourceTypeEnum.STOCK_SUBSCRIBE_REFUND,
                                reimburse,
                                "SUBSCRIBE_PARTIAL_REFUND_" + userStockSubscribe.getId(),
                                "新股部分中签返还多余资金");

                        if (!fundSourceResponse.isSuccess()) {
                            log.error("新股部分中签添加资金来源记录失败: {}", fundSourceResponse.getMsg());
                        } else {
                            log.info("新股部分中签添加资金来源记录成功");
                        }
                    }

                    return ServerResponse.createBySuccess("新股抢筹审核通过，以转入用户持仓，订单号：" + userPosition.getPositionSn());
                }
            }
        }
        userStockSubscribe.setStatus(Integer.valueOf(status));
        userStockSubscribe.setApplyNumber(Integer.valueOf(num));
        userStockSubscribe.setEndTime(new Date());
        int res = this.userStockSubscribeMapper.updateById(userStockSubscribe);
        if (res <= 0) {
            return ServerResponse.createByErrorMsg("修改状态失败");
        } else {
            return ServerResponse.createBySuccessMsg("修改状态成功");
        }
    }

    /**
     * 新股抢筹添加
     *
     * @param phone
     * @param code
     * @param num
     * @param request
     * @return
     */

    @Override
    public ServerResponse addQcByAdmin(String phone, String code, String num, HttpServletRequest request) {
        if (StringUtils.isEmpty(phone) || StringUtils.isEmpty(code) || StringUtils.isEmpty(num)) {
            return ServerResponse.createByErrorMsg("参数错误");
        }
        User user = userMapper.selectOne(new QueryWrapper<User>().eq("phone", phone));
        if (user == null) {
            return ServerResponse.createByErrorMsg("用户不存在");
        }
        StockSubscribe stockSubscribe =
                stockSubscribeMapper.selectOne(new QueryWrapper<StockSubscribe>().eq("code", code));
        if (stockSubscribe == null) {
            return ServerResponse.createByErrorMsg("新股代码不存在");
        }
        UserStockSubscribe userStockSubscribe = new UserStockSubscribe();
        userStockSubscribe.setOrderNo(KeyUtils.getUniqueKey());
        userStockSubscribe.setUserId(user.getId());
        userStockSubscribe.setRealName(user.getRealName() == null ? "模拟用户无实名" : user.getRealName());
        userStockSubscribe.setPhone(user.getPhone());
        userStockSubscribe.setAgentId(user.getAgentId());
        userStockSubscribe.setAgentName(user.getAgentName());
        userStockSubscribe.setNewCode(stockSubscribe.getCode());
        userStockSubscribe.setNewName(stockSubscribe.getName());
        userStockSubscribe.setBond(new BigDecimal(num).multiply(stockSubscribe.getPrice()));
        userStockSubscribe.setBuyPrice(stockSubscribe.getPrice());
        userStockSubscribe.setApplyNums(Integer.valueOf(num));
        userStockSubscribe.setType(3);
        userStockSubscribe.setStatus(1);
        userStockSubscribe.setAddTime(new Date());
        userStockSubscribe.setNewType(stockSubscribe.getStockType());
        int ret = userStockSubscribeMapper.insert(userStockSubscribe);
        if (ret > 0) {
            return ServerResponse.createBySuccessMsg("添加成功");
        } else {
            return ServerResponse.createByErrorMsg("添加失败");
        }
    }

    @Override
    public ServerResponse insertForUser(String userPhone, String newCode, Integer applyNums, Integer type, HttpServletRequest request) {
        // 参数校验
        if (StringUtils.isEmpty(userPhone) || StringUtils.isEmpty(newCode) || applyNums == null || applyNums <= 0 || type == null) {
            return ServerResponse.createByErrorMsg("参数错误，请检查用户手机号、新股代码、申购数量和申购类型");
        }

        // 查找用户
        User user = userMapper.selectOne(new QueryWrapper<User>().eq("phone", userPhone));
        if (user == null) {
            return ServerResponse.createByErrorMsg("用户不存在，请检查手机号是否正确");
        }

        // 查找新股信息
        StockSubscribe stockSubscribe = stockSubscribeMapper.selectOne(new QueryWrapper<StockSubscribe>().eq("code", newCode));
        if (stockSubscribe == null) {
            return ServerResponse.createByErrorMsg("新股代码不存在，请检查代码是否正确");
        }

        // 检查新股是否处于可申购状态
//        if (stockSubscribe.getZt() != null && stockSubscribe.getZt() != 1) {
//            return ServerResponse.createByErrorMsg("该新股当前不可申购");
//        }

//        if (stockSubscribe.getIsLock() != null && stockSubscribe.getIsLock() == 1) {
//            return ServerResponse.createByErrorMsg("该新股已被锁定，不可申购");
//        }

        // 获取优先价格（折扣价或发行价）
        BigDecimal buyPrice = Optional.ofNullable(stockSubscribe.getDiscountedPrice()).orElse(stockSubscribe.getPrice());

        // 计算保证金
        BigDecimal bond = new BigDecimal(applyNums).multiply(buyPrice);

        // 如果是类型2（需要扣款），检查用户余额并执行扣款
        if (type == 2) {
            if (user.getEnableAmt().compareTo(bond) < 0) {
                return ServerResponse.createByErrorMsg("用户可用余额不足，当前余额：" + user.getEnableAmt() + "，所需金额：" + bond);
            }

            // 执行扣款
            user.setEnableAmt(user.getEnableAmt().subtract(bond));
            if (user.getDjzj() != null) {
                user.setDjzj(user.getDjzj().add(bond));
            } else {
                user.setDjzj(bond);
            }

            int updateResult = userMapper.updateById(user);
            if (updateResult <= 0) {
                return ServerResponse.createByErrorMsg("用户余额更新失败，申购失败");
            }
        }

        // 创建申购记录
        UserStockSubscribe userStockSubscribe = new UserStockSubscribe();
        userStockSubscribe.setOrderNo(KeyUtils.getUniqueKey());
        userStockSubscribe.setUserId(user.getId());
        userStockSubscribe.setRealName(Objects.equals(user.getRealName(), "") || user.getRealName() == null ? "模拟用户无实名" : user.getRealName());
        userStockSubscribe.setPhone(user.getPhone());
        userStockSubscribe.setAgentId(user.getAgentId());
        userStockSubscribe.setAgentName(user.getAgentName());
        userStockSubscribe.setNewCode(stockSubscribe.getCode());
        userStockSubscribe.setNewName(stockSubscribe.getName());
        userStockSubscribe.setNewType(stockSubscribe.getStockType());
        userStockSubscribe.setBuyPrice(buyPrice);
        userStockSubscribe.setApplyNums(applyNums);
        userStockSubscribe.setBond(bond);
        userStockSubscribe.setType(type);
        userStockSubscribe.setStatus(1); // 默认状态为待审核
        userStockSubscribe.setAddTime(new Date());
        userStockSubscribe.setRemarks("后台管理员代为申购");

        // 插入数据库
        int insertResult = userStockSubscribeMapper.insert(userStockSubscribe);
        if (insertResult > 0) {
            return ServerResponse.createBySuccessMsg("代为申购成功，申购数量：" + applyNums + "，保证金：" + bond);
        } else {
            // 如果插入失败且已经扣款，需要回滚用户余额
            if (type == 2) {
                user.setEnableAmt(user.getEnableAmt().add(bond));
                user.setDjzj(user.getDjzj().subtract(bond));
                userMapper.updateById(user);
            }
            return ServerResponse.createByErrorMsg("申购失败，请稍后重试");
        }
    }

    @Override
    public ServerResponse getzqjkl(Integer status, HttpServletRequest request) {
        User user = this.iUserService.getCurrentRefreshUser(request);
        if (user == null) {
            return ServerResponse.createByErrorMsg("用户未登录");
        }
        QueryWrapper<UserStockSubscribe> userStockSubscribeQueryWrapper =
                new QueryWrapper<UserStockSubscribe>().eq("phone", user.getPhone());
        switch (status) {
            case 1:
            case 2:
                userStockSubscribeQueryWrapper = userStockSubscribeQueryWrapper.eq("status", status);
                break;
            case 3:
                userStockSubscribeQueryWrapper = userStockSubscribeQueryWrapper.ge("status", status);
                break;
            default:
                break;
        }
        List<UserStockSubscribe> userStockSubscribes =
                userStockSubscribeMapper.selectList(userStockSubscribeQueryWrapper.orderByDesc("add_time"));
        if (CollectionUtil.isNotEmpty(userStockSubscribes)) {
            userStockSubscribes.forEach(item -> {
                if (Objects.nonNull(item.getApplyNumber())) {
                    item.setBond(item.getBuyPrice().multiply(BigDecimal.valueOf(item.getApplyNumber())));
                }
            });
        }
        return ServerResponse.createBySuccess(userStockSubscribes);
    }

    @Override
    public ServerResponse<PageInfo> findByPage(int pageNum, int pageSize, Integer accountType, String realName,
                                               String phone, int status, Integer type, HttpServletRequest request) {
        if (StrUtil.isNotEmpty(realName)) {
            realName = realName.trim();
        }
        if (StrUtil.isNotEmpty(phone)) {
            phone = phone.trim();
        }
        Integer agentId = null;
        AgentUser currentAgentUser = UserInfoUtil.getCurrentAgentUser(request);
        if (Objects.nonNull(currentAgentUser)) {
            agentId = currentAgentUser.getId();
        }
        List<User> users = userMapper
                .selectList(new LambdaQueryWrapper<User>().like(StrUtil.isNotEmpty(realName), User::getRealName, realName)
                        .like(StrUtil.isNotEmpty(phone), User::getPhone, phone)
                        .eq(Objects.nonNull(agentId), User::getAgentId, agentId)
                        .eq(Objects.nonNull(accountType), User::getAccountType, accountType));

        // List<User> users = this.userMapper.listByAdmin(realName, phone, null, accountType);
        List<Integer> userIds = null;
        if (CollectionUtil.isNotEmpty(users)) {
            userIds = users.stream().map(User::getId).collect(Collectors.toList());
        }
        log.info("getStockSubscribeList,userIds={}", JSONUtil.toJsonStr(userIds));
        PageHelper.startPage(pageNum, pageSize);
        List<UserStockSubscribe> listData =
                userStockSubscribeMapper.selectList(new LambdaQueryWrapper<UserStockSubscribe>()
                        .like(StrUtil.isNotEmpty(realName), UserStockSubscribe::getRealName, realName)
                        .like(StrUtil.isNotEmpty(phone), UserStockSubscribe::getPhone, phone)
                        .eq(status != 0, UserStockSubscribe::getStatus, status)
                        .in(CollectionUtil.isNotEmpty(userIds), UserStockSubscribe::getUserId, userIds)
                        .eq(Objects.nonNull(type), UserStockSubscribe::getType, type)
                        .orderByDesc(UserStockSubscribe::getAddTime));
        PageInfo pageInfo = new PageInfo(listData);
        pageInfo.setList(listData);
        return ServerResponse.createBySuccess(pageInfo);
    }

    @Transactional
    @Override
    public ServerResponse virtualOneClickWin() {
        // 筛选虚拟用户
        List<User> users = this.userMapper
                .selectList(new LambdaQueryWrapper<User>().eq(User::getAccountType, NumberUtils.INTEGER_ONE));
        List<Integer> userIds = null;
        if (CollectionUtil.isNotEmpty(users)) {
            userIds = users.stream().map(User::getId).collect(Collectors.toList());
        } else {
            return ServerResponse.createByErrorMsg("无模拟用户");
        }
        log.info("virtualOneClickWin,userIds={}", JSONUtil.toJsonStr(userIds));
        // 1、待审核
        List<UserStockSubscribe> listData =
                userStockSubscribeMapper.selectList(new LambdaQueryWrapper<UserStockSubscribe>()
                        .in(UserStockSubscribe::getUserId, userIds).eq(UserStockSubscribe::getStatus, NumberUtils.INTEGER_ONE));
        log.info("virtualOneClickWin,listData={}", JSONUtil.toJsonStr(listData));
        if (CollectionUtil.isNotEmpty(listData)) {
            for (UserStockSubscribe userStockSubscribe : listData) {
                try {
                    if (Objects.isNull(userStockSubscribe.getApplyNums()) || userStockSubscribe.getApplyNums() == 0) {
                        // 申请数量为0，未中签
                        userStockSubscribe.setStatus(2);

                        // 如果是扫款申购（type=2），需要返还申购资金
                        if (userStockSubscribe.getType() != null && userStockSubscribe.getType() == 2) {
                            User user = userMapper.selectByPrimaryKey(userStockSubscribe.getUserId());
                            if (user != null && userStockSubscribe.getBond() != null && userStockSubscribe.getBond().compareTo(BigDecimal.ZERO) > 0) {
                                BigDecimal refundAmount = userStockSubscribe.getBond();
                                // 更新用户资金
                                user.setEnableAmt(user.getEnableAmt().add(refundAmount));
                                if (user.getDjzj() != null && user.getDjzj().compareTo(refundAmount) >= 0) {
                                    user.setDjzj(user.getDjzj().subtract(refundAmount));
                                } else {
                                    user.setDjzj(BigDecimal.ZERO);
                                }

                                int updateUserResult = userMapper.updateByPrimaryKeySelective(user);
                                if (updateUserResult > 0) {
                                    // 添加资金来源记录（新股未中签返还）
                                    ServerResponse fundSourceResponse = iUserFundSourceService.addFundSource(
                                            user.getId(),
                                            FundSourceTypeEnum.STOCK_SUBSCRIBE_REFUND,
                                            refundAmount,
                                            "SUBSCRIBE_REFUND_" + userStockSubscribe.getId(),
                                            "新股申购未中签返还");

                                    if (!fundSourceResponse.isSuccess()) {
                                        log.error("新股未中签添加资金来源记录失败: {}", fundSourceResponse.getMsg());
                                    } else {
                                        log.info("新股未中签添加资金来源记录成功");
                                    }
                                }
                            }
                        }

                        SiteMessage siteMessage = new SiteMessage();
                        siteMessage.setUserId(userStockSubscribe.getUserId());
                        siteMessage.setUserName(userStockSubscribe.getRealName());
                        siteMessage.setTypeName("新股申购");
                        siteMessage.setStatus(1);
                        String content = "【新股申购未中签】很遗憾，您的新股申购本次未中签";
                        if (userStockSubscribe.getType() != null && userStockSubscribe.getType() == 2 && userStockSubscribe.getBond() != null) {
                            content += "，申购金额：" + userStockSubscribe.getBond() + "已退还";
                        }
                        content += "。";
                        siteMessage.setContent(content);
                        siteMessage.setAddTime(DateTimeUtil.getCurrentDate());
                        iSiteMessageService.insert(siteMessage);
                    } else {
                        // 3、已中签
                        userStockSubscribe.setStatus(3);

                        SiteMessage siteMessage = new SiteMessage();
                        siteMessage.setUserId(userStockSubscribe.getUserId());
                        siteMessage.setUserName(userStockSubscribe.getRealName());
                        siteMessage.setTypeName("新股申购");
                        siteMessage.setStatus(1);
                        siteMessage.setContent("【新股申购中签】恭喜您，新股申购中签成功，请及时关注哦。");
                        siteMessage.setAddTime(DateTimeUtil.getCurrentDate());
                        iSiteMessageService.insert(siteMessage);
                    }
                    // 中签数量等于申购数量
                    userStockSubscribe.setApplyNumber(userStockSubscribe.getApplyNums());
                    userStockSubscribe.setEndTime(DateTimeUtil.getCurrentDate());
                    userStockSubscribeMapper.update1(userStockSubscribe);
                } catch (Exception e) {
                    log.error("virtualOneClickWin-Exception:", e);
                    return ServerResponse.createByErrorMsg("未知原因，审核失败，请联系技术");
                }

            }

        }
        return ServerResponse.createBySuccessMsg("模拟用户新股申购中签审核完成");
    }

    @Override
    public ServerResponse getAllRecord(HttpServletRequest request, Integer pageSize, Integer pageNum, Integer status) {
        User user = this.iUserService.getCurrentRefreshUser(request);
        if (user == null) {
            return ServerResponse.createByErrorMsg("用户未登录");
        }
        PageHelper.startPage(pageNum, pageSize);
        List<UserStockSubscribe> userStockSubscribes =
                userStockSubscribeMapper.selectList(new QueryWrapper<UserStockSubscribe>().eq("user_id", user.getId())
                        .eq(Objects.nonNull(status), "status", status).orderByDesc("add_time"));
        List<UserStockSubscribeVO> listData = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(userStockSubscribes)) {
            userStockSubscribes.forEach(item -> {
                UserStockSubscribeVO userStockSubscribeVO = new UserStockSubscribeVO();
                StockSubscribe stockSubscribe = stockSubscribeMapper
                        .selectOne(new QueryWrapper<>(new StockSubscribe()).eq("code", item.getNewCode()));
                if (Objects.nonNull(item.getApplyNumber())) {
                    item.setBond(item.getBuyPrice().multiply(BigDecimal.valueOf(item.getApplyNumber())));
                }
                userStockSubscribeVO.setId(item.getId());
                userStockSubscribeVO.setApplyNumber(item.getApplyNumber());
                userStockSubscribeVO.setApplyNums(item.getApplyNums());
                userStockSubscribeVO.setUserId(item.getUserId());
                userStockSubscribeVO.setBond(item.getBond());
                userStockSubscribeVO.setAddTime(item.getAddTime());
                userStockSubscribeVO.setBuyPrice(item.getBuyPrice());
                userStockSubscribeVO.setStatus(item.getStatus());

                userStockSubscribeVO.setEndTime(item.getEndTime());
                userStockSubscribeVO.setFixTime(item.getFixTime());

                userStockSubscribeVO.setNewName(stockSubscribe.getName());
                userStockSubscribeVO.setNewCode(stockSubscribe.getCode());
                // 发行数量
                userStockSubscribeVO.setOrderNumber(stockSubscribe.getOrderNumber());
                // 中签率
                userStockSubscribeVO.setWinningRate(stockSubscribe.getWinningRate());

                listData.add(userStockSubscribeVO);
            });
        }
        PageInfo pageInfo = new PageInfo(userStockSubscribes);
        pageInfo.setList(listData);
        return ServerResponse.createBySuccess(pageInfo);
    }

}